<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>FrontendApp10</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
  <app-root></app-root>
  <!-- Navigation -->
<nav style="padding: 20px; background-color: #f5f5f5; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
  <div style="max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between;">
    <h1 style="margin: 0; color: #333; font-size: 1.5rem;">Angular Material Demo App</h1>
    <div style="display: flex; align-items: center; gap: 20px;">
      <a routerLink="/demo" style="text-decoration: none; color: #1976d2; font-weight: bold; padding: 8px 16px; border-radius: 4px;">Demo Component</a>
      
      @if (authService.isLoggedIn()) {
        <span style="color: #666;">Xin chào, {{ authService.currentUser()?.username }}</span>
        <button (click)="logout()" style="background: #f44336; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Đăng xuất</button>
      } @else {
        <a routerLink="/login" style="text-decoration: none; color: #1976d2; font-weight: bold; padding: 8px 16px; border-radius: 4px;">Đăng nhập</a>
      }
    </div>
  </div>
</nav>

<!-- Router Outlet -->
<div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
  <router-outlet />
</div>


</body>
</html>
