<div style="padding: 20px; display: flex; justify-content: center;">
  <mat-card style="max-width: 600px; width: 100%;">
    <mat-card-header>
      <mat-card-title>Demo Angular Material</mat-card-title>
      <mat-card-subtitle>Bởi Phatdevio - Ví dụ về Material Design</mat-card-subtitle>
    </mat-card-header>

    <img mat-card-image src="https://picsum.photos/600/300" alt="Demo image" style="height: 300px; object-fit: cover;">

    <mat-card-content style="padding: 16px;">
      <p style="margin-bottom: 16px; font-size: 16px; line-height: 1.5;">
        Đ<PERSON>y là một ví dụ sử dụng <strong>Angular Material Card</strong>. 
        Bạn có thể thêm ảnh, tiêu đề, nội dung và các nút hành động để tạo ra 
        một giao diện đẹp và thân thiện với người dùng.
      </p>
      <p style="color: #666; font-size: 14px;">
        Angular Material cung cấp các component UI hiện đại theo chuẩn Material Design của Google.
      </p>
    </mat-card-content>

    <mat-card-actions style="padding: 8px 16px;">
      <button mat-raised-button color="primary" style="margin-right: 8px;">THÍCH</button>
      <button mat-raised-button color="accent">CHIA SẺ</button>
    </mat-card-actions>
  </mat-card>



</div>

