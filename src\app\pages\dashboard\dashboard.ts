import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.html',
  styleUrls: ['./dashboard.css']
})
export class DashboardComponent implements OnInit {
  userProfile: any = null;
  userList: any[] = [];
  loading = false;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/login']);
    }
  }

  getUserProfile(): void {
    this.loading = true;
    this.authService.getUserProfile().subscribe({
      next: (data) => {
        this.userProfile = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error fetching profile:', error);
        this.loading = false;
      }
    });
  }

  getUserList(): void {
    this.loading = true;
    this.authService.getUserList().subscribe({
      next: (data) => {
        this.userList = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error fetching user list:', error);
        this.loading = false;
      }
    });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
